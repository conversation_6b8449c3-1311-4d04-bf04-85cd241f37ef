# Implementation Plan: Location Group Role Assignments REST API

## Overview
This document provides a detailed step-by-step implementation plan for creating REST endpoints to manage user-to-location group CRUD operations with role assignments. The implementation follows the existing patterns in the Synapse ITS Go microservices architecture.

## Background Analysis

### Database Schema Understanding
The `LocationGroupRoleAssignments` table has a composite primary key consisting of:
- `MembershipId` (UUID) - Links to user's membership in the organization
- `LocationGroupId` (UUID) - The location group being assigned
- `RoleId` (UUID) - The role being granted within that location group

This design allows users to have multiple roles within the same location group by creating multiple records.

### Existing Architecture Patterns
Based on analysis of the codebase, the following patterns are established:
1. **Domain-Nested REST API Structure**: `/api/v3/{domain}/{resource}` pattern
2. **Dependency Injection**: Handler functions accept dependencies for testability
3. **Shared Libraries**: Common functionality in `shared/rest/onramp/` packages
4. **Consistent Error Handling**: Standardized error responses and logging
5. **Database Operations**: Parameterized queries with proper transaction handling

## Implementation Steps

### Step 1: Create Package Structure

**Reason**: Follow established architectural patterns for maintainability and consistency

**Action**: Create new package for location group role assignments
```
shared/rest/onramp/locationgrouproleassignments/
├── schemas.go                    # Data structures and DTOs
├── locationgrouproleassignments.go  # Main handler implementations
├── errors.go                     # Domain-specific errors
└── locationgrouproleassignments_test.go  # Comprehensive tests
```

**Dependencies**: 
- `shared/connect` for database connections
- `shared/api/response` for standardized responses
- `shared/logger` for logging
- `shared/rest/onramp/helper` for common utilities

### Step 2: Define Data Schemas

**Reason**: Establish consistent data structures for API requests and responses

**Schemas Required**:

```go
// LocationGroupRoleAssignment represents the core entity
type LocationGroupRoleAssignment struct {
    MembershipId    uuid.UUID `json:"membershipId" db:"membershipid"`
    LocationGroupId uuid.UUID `json:"locationGroupId" db:"locationgroupid"`
    RoleId          uuid.UUID `json:"roleId" db:"roleid"`
    IsDeleted       bool      `json:"-" db:"isdeleted"`
    CreatedAt       time.Time `json:"createdAt" db:"createdat"`
    UpdatedAt       time.Time `json:"updatedAt" db:"updatedat"`
}

// CreateLocationGroupRoleAssignmentRequest for POST operations
type CreateLocationGroupRoleAssignmentRequest struct {
    LocationGroupId uuid.UUID `json:"locationGroupId" validate:"required"`
    RoleId          uuid.UUID `json:"roleId" validate:"required"`
}

// UpdateLocationGroupRoleAssignmentRequest for PATCH operations
type UpdateLocationGroupRoleAssignmentRequest struct {
    RoleId uuid.UUID `json:"roleId" validate:"required"`
}

// LocationGroupRoleAssignmentResponse with enriched data
type LocationGroupRoleAssignmentResponse struct {
    MembershipId      uuid.UUID `json:"membershipId"`
    LocationGroupId   uuid.UUID `json:"locationGroupId"`
    LocationGroupName string    `json:"locationGroupName"`
    RoleId            uuid.UUID `json:"roleId"`
    RoleName          string    `json:"roleName"`
    CreatedAt         time.Time `json:"createdAt"`
    UpdatedAt         time.Time `json:"updatedAt"`
}
```

### Step 3: Define Error Handling

**Reason**: Consistent error handling across the API following established patterns

**Errors to Define**:
```go
var (
    ErrLocationGroupRoleAssignmentNotFound = errors.New("location group role assignment not found")
    ErrLocationGroupRoleAssignmentExists   = errors.New("location group role assignment already exists")
    ErrMembershipNotFound                  = errors.New("membership not found")
    ErrLocationGroupNotFound               = errors.New("location group not found")
    ErrRoleNotFound                        = errors.New("role not found")
    ErrInvalidLocationGroupId              = errors.New("invalid location group id")
    ErrInvalidRoleId                       = errors.New("invalid role id")
    ErrDatabaseOperation                   = errors.New("database operation failed")
)
```

### Step 4: Implement Handler Dependencies Structure

**Reason**: Enable dependency injection for testability following established patterns

```go
type HandlerDeps struct {
    GetConnections                           func(context.Context, ...bool) (*connect.Connections, error)
    CreateLocationGroupRoleAssignment        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error)
    GetLocationGroupRoleAssignmentsByUser    func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (*[]LocationGroupRoleAssignmentResponse, error)
    UpdateLocationGroupRoleAssignment        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, uuid.UUID, uuid.UUID, *UpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error)
    DeleteLocationGroupRoleAssignment        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, uuid.UUID, uuid.UUID) error
    ValidateMembershipExists                 func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (uuid.UUID, error)
    ValidateLocationGroupExists              func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
    ValidateRoleExists                       func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
}
```

### Step 5: Implement REST Handlers

**Reason**: Provide CRUD operations following RESTful principles and existing patterns

**URL Structure**: `/api/organizations/{organizationId}/users/{userId}/locationgroups`

#### 5.1 Create Assignment Handler
- **Method**: POST
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups`
- **Purpose**: Assign a user to a location group with a specific role
- **Validation**: 
  - Organization exists and is not deleted
  - User has membership in the organization
  - Location group exists and belongs to organization
  - Role exists and belongs to organization
  - Assignment doesn't already exist

#### 5.2 Get Assignments Handler  
- **Method**: GET
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups`
- **Purpose**: Retrieve all location group assignments for a user
- **Response**: Array of enriched assignment data with location group and role names

#### 5.3 Update Assignment Handler
- **Method**: PATCH  
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}`
- **Purpose**: Update the role for an existing assignment
- **Validation**: Assignment exists and new role is valid

#### 5.4 Delete Assignment Handler
- **Method**: DELETE
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}`  
- **Purpose**: Remove user from location group (soft delete)
- **Note**: Supports multiple role deletion by setting IsDeleted=true for all matching records

### Step 6: Database Operations Implementation

**Reason**: Efficient and secure database operations with proper error handling

#### 6.1 Create Assignment Operation
```sql
-- First ensure membership exists, create if needed
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId, IsDeleted, CreatedAt, UpdatedAt)
SELECT uuid_generate_v4(), am.Id, $2, false, NOW(), NOW()
FROM {{AuthMethod}} am
WHERE am.UserId = $1 AND NOT am.IsDeleted
ON CONFLICT (AuthMethodId, OrganizationId) DO NOTHING
RETURNING Id;

-- Then create the location group role assignment
INSERT INTO {{LocationGroupRoleAssignments}} (
    MembershipId, LocationGroupId, RoleId, IsDeleted, CreatedAt, UpdatedAt
) VALUES ($1, $2, $3, false, NOW(), NOW())
RETURNING MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt;
```

#### 6.2 Get Assignments Operation
```sql
SELECT 
    lgra.MembershipId,
    lgra.LocationGroupId,
    lg.Name as LocationGroupName,
    lgra.RoleId,
    cr.Name as RoleName,
    lgra.CreatedAt,
    lgra.UpdatedAt
FROM {{LocationGroupRoleAssignments}} lgra
JOIN {{Memberships}} m ON m.Id = lgra.MembershipId
JOIN {{AuthMethod}} am ON am.Id = m.AuthMethodId
JOIN {{LocationGroups}} lg ON lg.Id = lgra.LocationGroupId
JOIN {{CustomRole}} cr ON cr.Id = lgra.RoleId
WHERE am.UserId = $1 
  AND m.OrganizationId = $2 
  AND NOT lgra.IsDeleted 
  AND NOT m.IsDeleted 
  AND NOT am.IsDeleted
  AND NOT lg.IsDeleted 
  AND NOT cr.IsDeleted;
```

### Step 7: Register Routes in Organization Handler

**Reason**: Integrate with existing routing structure in onramp microservice

**Location**: `microservices/onramp/modules/organization/handler.go`

**Routes to Add**:
```go
// Location Group Role Assignments CRUD
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups", 
    RestLocationGroupRoleAssignments.CreateLocationGroupRoleAssignmentHandler).Methods(http.MethodPost)
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups", 
    RestLocationGroupRoleAssignments.GetLocationGroupRoleAssignmentsHandler).Methods(http.MethodGet)
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}", 
    RestLocationGroupRoleAssignments.UpdateLocationGroupRoleAssignmentHandler).Methods(http.MethodPatch)
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}", 
    RestLocationGroupRoleAssignments.DeleteLocationGroupRoleAssignmentHandler).Methods(http.MethodDelete)
```

### Step 8: Implement Comprehensive Testing

**Reason**: Ensure reliability and maintainability through thorough test coverage

**Test Categories**:
1. **Unit Tests**: Individual function testing with mocked dependencies
2. **Integration Tests**: Database operation testing
3. **Handler Tests**: HTTP request/response testing
4. **Error Scenario Tests**: Validation and error handling testing

**Key Test Scenarios**:
- Valid CRUD operations
- Invalid UUID parameters
- Non-existent resources
- Duplicate assignment creation
- Permission validation
- Database error handling
- Concurrent access scenarios

### Step 9: Documentation and Validation

**Reason**: Ensure proper integration and usage understanding

**Documentation Required**:
1. API endpoint documentation with request/response examples
2. Database schema impact documentation
3. Integration guide for frontend consumers
4. Error code reference

**Validation Steps**:
1. Run comprehensive test suite
2. Verify database constraints work correctly
3. Test API endpoints with various scenarios
4. Validate error responses match expected format
5. Confirm logging provides adequate debugging information

## Technical Considerations

### Security
- All operations require valid organization membership
- Role assignments are scoped to organization context
- Soft deletes maintain audit trail
- Input validation prevents injection attacks

### Performance
- Indexed foreign key relationships for efficient queries
- Composite primary key enables fast lookups
- Minimal data transfer with focused response schemas
- Connection pooling through shared connection management

### Scalability
- Stateless handler design supports horizontal scaling
- Database operations use parameterized queries
- Dependency injection enables easy testing and mocking
- Modular package structure supports independent development

### Maintainability
- Follows established architectural patterns
- Comprehensive error handling and logging
- Clear separation of concerns
- Extensive test coverage
- Consistent naming conventions

## Next Steps After Implementation

1. **Integration Testing**: Test with frontend applications
2. **Performance Testing**: Validate under expected load
3. **Security Review**: Ensure proper authorization checks
4. **Documentation Review**: Update API documentation
5. **Deployment Planning**: Coordinate with DevOps for rollout strategy

This implementation plan provides a comprehensive roadmap for creating robust, maintainable, and scalable location group role assignment REST endpoints that integrate seamlessly with the existing Synapse ITS microservices architecture.
