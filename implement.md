# Implementation Plan: Location Group Role Assignments REST API

## Overview
This document provides a detailed step-by-step implementation plan for creating REST endpoints to manage user-to-location group CRUD operations with role assignments. The implementation follows the existing patterns in the Synapse ITS Go microservices architecture.

## Background Analysis

### Database Schema Understanding
The `LocationGroupRoleAssignments` table has a composite primary key consisting of:
- `MembershipId` (UUID) - Links to user's membership in the organization
- `LocationGroupId` (UUID) - The location group being assigned
- `RoleId` (UUID) - The role being granted within that location group

**Multiple Roles Design Pattern**:
This design allows users to have multiple roles within the same location group by creating multiple records. Each unique combination of (MembershipId, LocationGroupId, RoleId) represents a separate assignment.

**Example Scenario**:
```
User: <PERSON> (MembershipId: abc-123)
Location Group: "Building A" (LocationGroupId: def-456)

Possible Role Assignments:
1. (abc-123, def-456, role-admin)    - <PERSON> has Admin role in Building A
2. (abc-123, def-456, role-viewer)   - <PERSON> has Viewer role in Building A
3. (abc-123, def-456, role-operator) - <PERSON> has Operator role in Building A
```

This approach provides maximum flexibility for permission management, allowing users to accumulate permissions from multiple roles within the same location group context.

### Existing Architecture Patterns
Based on analysis of the codebase, the following patterns are established:
1. **Domain-Nested REST API Structure**: `/api/v3/{domain}/{resource}` pattern
2. **Dependency Injection**: Handler functions accept dependencies for testability
3. **Shared Libraries**: Common functionality in `shared/rest/onramp/` packages
4. **Consistent Error Handling**: Standardized error responses and logging
5. **Database Operations**: Parameterized queries with proper transaction handling

## Implementation Steps

### Step 1: Create Package Structure

**Reason**: Follow established architectural patterns for maintainability and consistency

**Action**: Create new package for location group role assignments
```
shared/rest/onramp/locationgrouproleassignments/
├── schemas.go                    # Data structures and DTOs
├── locationgrouproleassignments.go  # Main handler implementations
├── errors.go                     # Domain-specific errors
└── locationgrouproleassignments_test.go  # Comprehensive tests
```

**Dependencies**: 
- `shared/connect` for database connections
- `shared/api/response` for standardized responses
- `shared/logger` for logging
- `shared/rest/onramp/helper` for common utilities

### Step 2: Define Data Schemas

**Reason**: Establish consistent data structures for API requests and responses

**Schemas Required**:

```go
// LocationGroupRoleAssignment represents the core entity
type LocationGroupRoleAssignment struct {
    MembershipId    uuid.UUID `json:"membershipId" db:"membershipid"`
    LocationGroupId uuid.UUID `json:"locationGroupId" db:"locationgroupid"`
    RoleId          uuid.UUID `json:"roleId" db:"roleid"`
    IsDeleted       bool      `json:"-" db:"isdeleted"`
    CreatedAt       time.Time `json:"createdAt" db:"createdat"`
    UpdatedAt       time.Time `json:"updatedAt" db:"updatedat"`
}

// CreateLocationGroupRoleAssignmentRequest for POST operations
type CreateLocationGroupRoleAssignmentRequest struct {
    LocationGroupId uuid.UUID `json:"locationGroupId" validate:"required"`
    RoleId          uuid.UUID `json:"roleId" validate:"required"`
}

// UpdateLocationGroupRoleAssignmentRequest for PATCH operations
type UpdateLocationGroupRoleAssignmentRequest struct {
    RoleId uuid.UUID `json:"roleId" validate:"required"`
}

// LocationGroupRoleAssignmentResponse with enriched data
type LocationGroupRoleAssignmentResponse struct {
    MembershipId      uuid.UUID `json:"membershipId"`
    LocationGroupId   uuid.UUID `json:"locationGroupId"`
    LocationGroupName string    `json:"locationGroupName"`
    RoleId            uuid.UUID `json:"roleId"`
    RoleName          string    `json:"roleName"`
    CreatedAt         time.Time `json:"createdAt"`
    UpdatedAt         time.Time `json:"updatedAt"`
}
```

### Step 3: Define Error Handling

**Reason**: Consistent error handling across the API following established patterns

**Errors to Define**:
```go
var (
    ErrLocationGroupRoleAssignmentNotFound = errors.New("location group role assignment not found")
    ErrLocationGroupRoleAssignmentExists   = errors.New("location group role assignment already exists")
    ErrMembershipNotFound                  = errors.New("membership not found")
    ErrLocationGroupNotFound               = errors.New("location group not found")
    ErrRoleNotFound                        = errors.New("role not found")
    ErrInvalidLocationGroupId              = errors.New("invalid location group id")
    ErrInvalidRoleId                       = errors.New("invalid role id")
    ErrDatabaseOperation                   = errors.New("database operation failed")
)
```

### Step 4: Implement Handler Dependencies Structure

**Reason**: Enable dependency injection for testability following established patterns

```go
type HandlerDeps struct {
    GetConnections                           func(context.Context, ...bool) (*connect.Connections, error)

    // Core CRUD operations supporting multiple roles
    CreateLocationGroupRoleAssignment        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error)
    GetLocationGroupRoleAssignmentsByUser    func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (*[]LocationGroupRoleAssignmentResponse, error)

    // Multiple role management operations
    DeleteSingleRoleAssignment               func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, uuid.UUID, uuid.UUID) error
    DeleteAllRoleAssignments                 func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, uuid.UUID) error
    CheckRoleAssignmentExists                func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, uuid.UUID, uuid.UUID) (bool, error)
    GetRoleAssignmentsByLocationGroup        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, uuid.UUID) (*[]LocationGroupRoleAssignmentResponse, error)

    // Validation functions
    ValidateMembershipExists                 func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (uuid.UUID, error)
    ValidateLocationGroupExists              func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
    ValidateRoleExists                       func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
    ValidateRoleDependencies                 func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, uuid.UUID, uuid.UUID) error
}
```

### Step 5: Implement REST Handlers

**Reason**: Provide CRUD operations following RESTful principles and existing patterns

**URL Structure**: `/api/organizations/{organizationId}/users/{userId}/locationgroups`

#### 5.1 Create Assignment Handler
- **Method**: POST
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups`
- **Purpose**: Assign a user to a location group with a specific role
- **Multiple Roles Support**: This endpoint can be called multiple times with different roleId values to assign multiple roles to the same user in the same location group
- **Validation**:
  - Organization exists and is not deleted
  - User has membership in the organization
  - Location group exists and belongs to organization
  - Role exists and belongs to organization
  - Specific (MembershipId, LocationGroupId, RoleId) combination doesn't already exist
- **Example Request Bodies**:
  ```json
  // First call - Assign Admin role
  {
    "locationGroupId": "def-456",
    "roleId": "role-admin-uuid"
  }

  // Second call - Assign Viewer role to same user/location group
  {
    "locationGroupId": "def-456",
    "roleId": "role-viewer-uuid"
  }
  ```

#### 5.2 Get Assignments Handler
- **Method**: GET
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups`
- **Purpose**: Retrieve all location group assignments for a user
- **Multiple Roles Handling**: Returns separate records for each role assignment, even if they're for the same location group
- **Response**: Array of enriched assignment data with location group and role names
- **Example Response**:
  ```json
  [
    {
      "membershipId": "abc-123",
      "locationGroupId": "def-456",
      "locationGroupName": "Building A",
      "roleId": "role-admin-uuid",
      "roleName": "Administrator",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    },
    {
      "membershipId": "abc-123",
      "locationGroupId": "def-456",
      "locationGroupName": "Building A",
      "roleId": "role-viewer-uuid",
      "roleName": "Viewer",
      "createdAt": "2024-01-15T11:00:00Z",
      "updatedAt": "2024-01-15T11:00:00Z"
    }
  ]
  ```

#### 5.3 Update Assignment Handler
- **Method**: PATCH
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}/roles/{roleId}`
- **Purpose**: Update a specific role assignment (e.g., change role permissions or metadata)
- **Multiple Roles Consideration**: URL includes roleId to target specific role assignment
- **Validation**: Specific assignment exists and new role data is valid
- **Note**: For changing roles, it's recommended to DELETE old role and CREATE new role to maintain audit trail

#### 5.4 Delete Assignment Handler - Single Role
- **Method**: DELETE
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}/roles/{roleId}`
- **Purpose**: Remove a specific role assignment from user in location group
- **Multiple Roles Handling**: Only deletes the specific role, leaving other roles intact

#### 5.5 Delete All Assignments Handler - All Roles
- **Method**: DELETE
- **URL**: `/api/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}`
- **Purpose**: Remove user from location group entirely (all roles)
- **Multiple Roles Handling**: Sets IsDeleted=true for ALL role assignments matching the user and location group
- **Use Case**: When completely removing user access to a location group

### Step 6: Database Operations Implementation

**Reason**: Efficient and secure database operations with proper error handling

#### 6.1 Create Assignment Operation
**Multiple Roles Support**: This operation can be called multiple times with different RoleId values to assign multiple roles to the same user in the same location group.

```sql
-- Step 1: Get or create membership for the user in the organization
WITH membership_upsert AS (
    INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId, IsDeleted, CreatedAt, UpdatedAt)
    SELECT uuid_generate_v4(), am.Id, $2, false, NOW(), NOW()
    FROM {{AuthMethod}} am
    WHERE am.UserId = $1 AND NOT am.IsDeleted
    ON CONFLICT (AuthMethodId, OrganizationId)
    DO UPDATE SET IsDeleted = false, UpdatedAt = NOW()
    RETURNING Id
),
existing_membership AS (
    SELECT m.Id
    FROM {{Memberships}} m
    JOIN {{AuthMethod}} am ON am.Id = m.AuthMethodId
    WHERE am.UserId = $1 AND m.OrganizationId = $2 AND NOT m.IsDeleted
)
-- Step 2: Create the specific role assignment (will fail if exact combination exists)
INSERT INTO {{LocationGroupRoleAssignments}} (
    MembershipId, LocationGroupId, RoleId, IsDeleted, CreatedAt, UpdatedAt
)
SELECT
    COALESCE(mu.Id, em.Id), $3, $4, false, NOW(), NOW()
FROM (SELECT Id FROM membership_upsert UNION SELECT Id FROM existing_membership) AS combined(Id)
LEFT JOIN membership_upsert mu ON mu.Id = combined.Id
LEFT JOIN existing_membership em ON em.Id = combined.Id
RETURNING MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt;
```

**Key Points**:
- Uses UPSERT pattern to handle existing memberships
- Composite primary key prevents duplicate (MembershipId, LocationGroupId, RoleId) combinations
- Allows multiple roles for same user/location group by varying RoleId

#### 6.2 Get Assignments Operation
**Multiple Roles Handling**: This query returns one row per role assignment, so users with multiple roles in the same location group will have multiple rows returned.

```sql
SELECT
    lgra.MembershipId,
    lgra.LocationGroupId,
    lg.Name as LocationGroupName,
    lgra.RoleId,
    cr.Name as RoleName,
    cr.Description as RoleDescription,
    lgra.CreatedAt,
    lgra.UpdatedAt,
    -- Additional context for UI display
    lg.OrganizationId,
    COUNT(*) OVER (PARTITION BY lgra.LocationGroupId) as TotalRolesInLocationGroup
FROM {{LocationGroupRoleAssignments}} lgra
JOIN {{Memberships}} m ON m.Id = lgra.MembershipId
JOIN {{AuthMethod}} am ON am.Id = m.AuthMethodId
JOIN {{LocationGroups}} lg ON lg.Id = lgra.LocationGroupId
JOIN {{CustomRole}} cr ON cr.Id = lgra.RoleId
WHERE am.UserId = $1
  AND m.OrganizationId = $2
  AND NOT lgra.IsDeleted
  AND NOT m.IsDeleted
  AND NOT am.IsDeleted
  AND NOT lg.IsDeleted
  AND NOT cr.IsDeleted
ORDER BY lg.Name, cr.Name; -- Group by location, then by role name
```

**Result Interpretation**:
- Each row represents one role assignment
- Same location group may appear in multiple rows if user has multiple roles
- Frontend can group by LocationGroupId to show consolidated view

#### 6.3 Delete Single Role Assignment Operation
```sql
-- Delete specific role assignment (soft delete)
UPDATE {{LocationGroupRoleAssignments}}
SET IsDeleted = true, UpdatedAt = NOW()
WHERE MembershipId = (
    SELECT m.Id
    FROM {{Memberships}} m
    JOIN {{AuthMethod}} am ON am.Id = m.AuthMethodId
    WHERE am.UserId = $1 AND m.OrganizationId = $2 AND NOT m.IsDeleted
)
AND LocationGroupId = $3
AND RoleId = $4
AND NOT IsDeleted;
```

#### 6.4 Delete All Role Assignments Operation
```sql
-- Delete all role assignments for user in specific location group
UPDATE {{LocationGroupRoleAssignments}}
SET IsDeleted = true, UpdatedAt = NOW()
WHERE MembershipId = (
    SELECT m.Id
    FROM {{Memberships}} m
    JOIN {{AuthMethod}} am ON am.Id = m.AuthMethodId
    WHERE am.UserId = $1 AND m.OrganizationId = $2 AND NOT m.IsDeleted
)
AND LocationGroupId = $3
AND NOT IsDeleted;
```

### Step 7: Register Routes in Organization Handler

**Reason**: Integrate with existing routing structure in onramp microservice

**Location**: `microservices/onramp/modules/organization/handler.go`

**Routes to Add**:
```go
// Location Group Role Assignments CRUD - Multiple Roles Support
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups",
    RestLocationGroupRoleAssignments.CreateLocationGroupRoleAssignmentHandler).Methods(http.MethodPost)
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups",
    RestLocationGroupRoleAssignments.GetLocationGroupRoleAssignmentsHandler).Methods(http.MethodGet)

// Specific role management
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}/roles/{roleId}",
    RestLocationGroupRoleAssignments.UpdateLocationGroupRoleAssignmentHandler).Methods(http.MethodPatch)
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}/roles/{roleId}",
    RestLocationGroupRoleAssignments.DeleteSingleRoleAssignmentHandler).Methods(http.MethodDelete)

// Bulk operations for all roles in a location group
router.HandleFunc("/organizations/{organizationId}/users/{userId}/locationgroups/{locationGroupId}",
    RestLocationGroupRoleAssignments.DeleteAllRoleAssignmentsHandler).Methods(http.MethodDelete)
```

### Step 8: Implement Comprehensive Testing

**Reason**: Ensure reliability and maintainability through thorough test coverage

**Test Categories**:
1. **Unit Tests**: Individual function testing with mocked dependencies
2. **Integration Tests**: Database operation testing
3. **Handler Tests**: HTTP request/response testing
4. **Error Scenario Tests**: Validation and error handling testing

**Key Test Scenarios**:
- Valid CRUD operations
- Invalid UUID parameters
- Non-existent resources
- Permission validation
- Database error handling
- Concurrent access scenarios

**Multiple Roles Specific Test Scenarios**:
- Assign multiple roles to same user/location group
- Verify duplicate role assignment prevention
- Test selective role removal (remove one role, keep others)
- Test bulk role removal (remove all roles from location group)
- Verify role dependency validation (if implemented)
- Test role assignment ordering and timestamps
- Concurrent role assignments to same user/location group
- Query performance with users having many roles
- Frontend grouping logic validation
- Permission accumulation from multiple roles

### Step 9: Documentation and Validation

**Reason**: Ensure proper integration and usage understanding

**Documentation Required**:
1. API endpoint documentation with request/response examples
2. Database schema impact documentation
3. Integration guide for frontend consumers
4. Error code reference

**Validation Steps**:
1. Run comprehensive test suite
2. Verify database constraints work correctly
3. Test API endpoints with various scenarios
4. Validate error responses match expected format
5. Confirm logging provides adequate debugging information

## Multiple Roles Pattern - Detailed Explanation

### Conceptual Overview
The requirement states: "a user may have more than one role, simply by adding multiple records to the table." This is implemented through the composite primary key design that allows multiple role assignments per user per location group.

### Database Design Benefits
1. **Granular Permission Control**: Users can accumulate permissions from multiple roles
2. **Audit Trail**: Each role assignment has its own creation/update timestamps
3. **Flexible Management**: Roles can be added/removed independently
4. **Performance**: Indexed composite key enables efficient queries

### Practical Use Cases

#### Scenario 1: Progressive Permission Accumulation
```
User: Jane Smith
Location Group: "Data Center Floor 1"

Initial Assignment:
- Role: "Viewer" (can see device status)

Later Addition:
- Role: "Operator" (can control devices)

Final State: Jane has both Viewer AND Operator permissions
```

#### Scenario 2: Temporary Additional Permissions
```
User: Bob Johnson
Location Group: "Manufacturing Line A"

Permanent Assignment:
- Role: "Technician" (maintenance permissions)

Temporary Assignment (during manager vacation):
- Role: "Supervisor" (approval permissions)

Later: Remove Supervisor role, keep Technician role
```

### API Workflow Examples

#### Adding Multiple Roles
```bash
# Step 1: Assign initial role
POST /api/organizations/org-123/users/user-456/locationgroups
{
  "locationGroupId": "lg-789",
  "roleId": "role-viewer"
}

# Step 2: Add additional role to same location group
POST /api/organizations/org-123/users/user-456/locationgroups
{
  "locationGroupId": "lg-789",
  "roleId": "role-operator"
}

# Step 3: Add third role
POST /api/organizations/org-123/users/user-456/locationgroups
{
  "locationGroupId": "lg-789",
  "roleId": "role-admin"
}
```

#### Querying Multiple Roles
```bash
GET /api/organizations/org-123/users/user-456/locationgroups

Response:
[
  {
    "membershipId": "membership-abc",
    "locationGroupId": "lg-789",
    "locationGroupName": "Data Center Floor 1",
    "roleId": "role-viewer",
    "roleName": "Viewer",
    "createdAt": "2024-01-15T10:00:00Z"
  },
  {
    "membershipId": "membership-abc",
    "locationGroupId": "lg-789",
    "locationGroupName": "Data Center Floor 1",
    "roleId": "role-operator",
    "roleName": "Operator",
    "createdAt": "2024-01-15T11:00:00Z"
  },
  {
    "membershipId": "membership-abc",
    "locationGroupId": "lg-789",
    "locationGroupName": "Data Center Floor 1",
    "roleId": "role-admin",
    "roleName": "Administrator",
    "createdAt": "2024-01-15T12:00:00Z"
  }
]
```

#### Selective Role Removal
```bash
# Remove only the admin role, keep viewer and operator
DELETE /api/organizations/org-123/users/user-456/locationgroups/lg-789/roles/role-admin

# Remove all roles from the location group
DELETE /api/organizations/org-123/users/user-456/locationgroups/lg-789
```

### Frontend Integration Considerations

#### Grouped Display Pattern
```javascript
// Frontend can group multiple roles by location group
const groupedAssignments = assignments.reduce((acc, assignment) => {
  const key = assignment.locationGroupId;
  if (!acc[key]) {
    acc[key] = {
      locationGroupId: assignment.locationGroupId,
      locationGroupName: assignment.locationGroupName,
      roles: []
    };
  }
  acc[key].roles.push({
    roleId: assignment.roleId,
    roleName: assignment.roleName,
    createdAt: assignment.createdAt
  });
  return acc;
}, {});
```

#### Permission Calculation
```javascript
// Calculate effective permissions from multiple roles
function calculateEffectivePermissions(userRoles) {
  return userRoles.reduce((permissions, role) => {
    return permissions.concat(role.permissions);
  }, []).filter((permission, index, self) =>
    self.indexOf(permission) === index // Remove duplicates
  );
}
```

### Error Handling for Multiple Roles

#### Duplicate Role Assignment
```json
// Attempting to assign same role twice
POST /api/organizations/org-123/users/user-456/locationgroups
{
  "locationGroupId": "lg-789",
  "roleId": "role-viewer" // Already assigned
}

Response: 409 Conflict
{
  "error": "Role assignment already exists",
  "details": "User already has role 'Viewer' in location group 'Data Center Floor 1'"
}
```

#### Role Dependency Validation
```json
// Business rule: Cannot remove Viewer role if user has Operator role
DELETE /api/organizations/org-123/users/user-456/locationgroups/lg-789/roles/role-viewer

Response: 400 Bad Request
{
  "error": "Cannot remove dependent role",
  "details": "Viewer role is required for Operator role. Remove Operator role first."
}
```

## Technical Considerations

### Security
- All operations require valid organization membership
- Role assignments are scoped to organization context
- Soft deletes maintain audit trail
- Input validation prevents injection attacks

### Performance
- Indexed foreign key relationships for efficient queries
- Composite primary key enables fast lookups
- Minimal data transfer with focused response schemas
- Connection pooling through shared connection management

### Scalability
- Stateless handler design supports horizontal scaling
- Database operations use parameterized queries
- Dependency injection enables easy testing and mocking
- Modular package structure supports independent development

### Maintainability
- Follows established architectural patterns
- Comprehensive error handling and logging
- Clear separation of concerns
- Extensive test coverage
- Consistent naming conventions

## Next Steps After Implementation

1. **Integration Testing**: Test with frontend applications
2. **Performance Testing**: Validate under expected load
3. **Security Review**: Ensure proper authorization checks
4. **Documentation Review**: Update API documentation
5. **Deployment Planning**: Coordinate with DevOps for rollout strategy

This implementation plan provides a comprehensive roadmap for creating robust, maintainable, and scalable location group role assignment REST endpoints that integrate seamlessly with the existing Synapse ITS microservices architecture.
