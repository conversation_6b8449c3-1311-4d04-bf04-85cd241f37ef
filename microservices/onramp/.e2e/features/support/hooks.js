const {
  setDefaultTimeout,
  Before,
  After,
  AfterStep
} = require('@cucumber/cucumber');
const { Builder } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');
const fs = require('fs');
const path = require('path');
const { waitForPageLoad } = require('./utils');

// To get the logging:
const { logging } = require('selenium-webdriver');
const prefs = new logging.Preferences();
prefs.setLevel(logging.Type.BROWSER, logging.Level.ALL);


// 1) Extend Cucumber's step timeout from 5s to 60s
setDefaultTimeout(60 * 1000);

Before(async function () {
  const options = new chrome.Options()
    .addArguments(
      '--headless=chrome',
      '--disable-gpu',
      '--no-sandbox',
      '--disable-dev-shm-usage',
      '--window-size=1920,1080'
    );

  // Build a single driver instance and attach to `this`
  this.driver = await new Builder()
    .forBrowser('chrome')
    .usingServer(`http://${process.env.SELENIUM_HOST}:${process.env.SELENIUM_PORT}`)
    .setChromeOptions(options)
    .setLoggingPrefs(prefs) // Turn on logging
    .build();

  // Optional: set implicit/pageLoad timeouts
  await this.driver.manage().setTimeouts({
    //implicit: 15000,
    pageLoad: 30000,
    script: 30000
  });
});

After(async function (scenario) {
  if (this.driver) {
    await this.driver.quit();
  }
});

// Take screenshots of test steps to use when testing
AfterStep(async function (step) {
  if (!this.driver) return;
  try {
    await waitForPageLoad(this.driver, 15000);
    const screenshot = await this.driver.takeScreenshot();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotDir = path.resolve('screenshots');
    fs.mkdirSync(screenshotDir, { recursive: true });
    const screenshotPath = path.join(screenshotDir, `${step.pickleStep.text}.png`);
    fs.writeFileSync(screenshotPath, screenshot, 'base64');
    console.log(`Screenshot saved: ${screenshotPath}`);
  } catch (error) {
    console.error('Failed to capture screenshot after step:', error);
  }
});
