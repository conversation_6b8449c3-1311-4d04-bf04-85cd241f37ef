import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, catchError, Observable, of, tap, throwError } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Router } from '@angular/router';

export interface UserProfile {
  sub: string;
  email: string;
  name?: string;
}

export interface User {
  username: string;
  email: string;
  firstName: string;
  lastname: string;
}

export interface Permission {
  scope: string;
  scopeId: string;
  organizationId: string;
  permissions: string[];
}

export interface PermissionsResponse {
  data: {
    userId: string;
    permissions: Permission[];
  };
}

@Injectable({ providedIn: 'root' })
export class AuthService {
  private userSubject = new BehaviorSubject<User | null>(null);
  user$ = this.userSubject.asObservable();
  private userKlSubject = new BehaviorSubject<UserProfile | null>(null);
  userKl$ = this.userKlSubject.asObservable();
  private permissionsSubject = new BehaviorSubject<PermissionsResponse | null>(null);
  permissions$ = this.permissionsSubject.asObservable();
  isValidLogin = false;

  constructor(
    private http: HttpClient,
    private message: NzMessageService,
    private router: Router,
  ) {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        this.userSubject.next(JSON.parse(storedUser));
      } catch (e) {
        localStorage.removeItem('user');
      }
    }
    const storedUserKl = localStorage.getItem('userKl');
    if (storedUserKl) {
      try {
        this.userKlSubject.next(JSON.parse(storedUserKl));
      } catch (e) {
        localStorage.removeItem('userKl');
      }
    }
    const storedPermissions = localStorage.getItem('permissions');
    if (storedPermissions) {
      try {
        this.permissionsSubject.next(JSON.parse(storedPermissions));
      } catch (e) {
        localStorage.removeItem('permissions');
      }
    }
  }

  getProfile(): Observable<UserProfile> {
    return this.http.get<UserProfile>('/protected/profile').pipe(
      tap((res: any) => {
        if (res) {
          localStorage.setItem('userKl', JSON.stringify(res));
          this.userKlSubject.next(res);
          this.router.navigate(['/']);
          this.getUserPermissions().subscribe();
        }
      }),
      catchError(error => {
        console.error('Login failed', error);
        return throwError(error);
      })
    );
  }

  login(): void {
    window.location.href = '/login';
  }

  logout(): void {
    window.location.href = '/logout';
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('userKl');
    localStorage.removeItem('permissions');
    localStorage.removeItem('organizationPermissionsSynapse');
    this.userSubject.next(null);
    this.userKlSubject.next(null);
    this.permissionsSubject.next(null);
  }

  getCookie(name: string): string | null {
    if (typeof document === 'undefined') {
      console.warn('document.cookie is not available in this environment');
      return null;
    }

    const nameEQ = `${encodeURIComponent(name)}=`;
    const cookies = document.cookie.split(';');

    for (let cookie of cookies) {
      cookie = cookie.trim();
      if (cookie.startsWith(nameEQ)) {
        return decodeURIComponent(cookie.substring(nameEQ.length)) || null;
      }
    }

    return null;
  }

  loginApi(credentials: { username: string; password: string }): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
    const body = new URLSearchParams();
    body.set('username', credentials.username);
    body.set('password', credentials.password);
    return this.http.post<any>('/login', body.toString(), { headers, observe: 'response' }).pipe(
      tap((res: any) => {
        if (res.status === 200) {
          this.getUserPermissions().subscribe();
          this.router.navigate(['/']);
          this.message.create('success', 'Login successfully!');
          this.isValidLogin = true;
          this.getProfile().subscribe();
        } else {
          throw new Error('Invalid login response');
        }
      }),
      catchError(error => {
        console.error('Login failed', error);
        this.message.create('error', 'Login failed. Please check your credentials.', { nzDuration: 5000 });
        return throwError(error);
      })
    );
  }

  getUserPermissions(): Observable<PermissionsResponse> {
    return this.http.get<PermissionsResponse>('/api/user/permissions').pipe(
      tap((res: PermissionsResponse) => {
        if (res) {
          localStorage.setItem('permissions', res.data.userId);
          this.permissionsSubject.next(res);
        }
      }),
      catchError(error => {
        console.error('Get User Permissions failed', error);
        this.message.create('error', 'Failed to load user permissions.', { nzDuration: 5000 });
        return throwError(error);
      })
    );
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  getUser(): string | null {
    return localStorage.getItem('user');
  }

  getUserAPI(): User | null {
    return this.userSubject.value;
  }

  registerUser(userData: { firstname: string, lastname: string, username: string, email: string, password: string }): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
    const body = new URLSearchParams();
    body.set('username', userData.username);
    body.set('password', userData.password);
    body.set('email', userData.email);
    body.set('firstname', userData.firstname);
    body.set('lastname', userData.lastname);
    return this.http.post<any>('/register', body.toString(), { headers }).pipe(
      tap((res: any) => {
        this.message.create('success', 'Registration successful! Redirecting to login...');
        this.router.navigate(['/login']);
      }),
      catchError(error => {
        this.message.create('error', 'Registration failed. Please try again.');
        return throwError(error);
      })
    );
  }
}