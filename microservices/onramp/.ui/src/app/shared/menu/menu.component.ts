import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { forkJoin, filter } from 'rxjs';
import { Organization, OrganizationsService } from '../../core/services/organization.service';
import { AuthService } from '../../core/services/auth.service';

interface ExtendedOrganization extends Organization {
  name: string;
  hasDeviceGroupScope: boolean;
  hasLocationGroupScope: boolean;
  hasOrgManageDevices: boolean;
}

@Component({
  selector: 'app-menu',
  standalone: false,
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.css'
})
export class MenuComponent implements OnInit {
  selectedKey: string | null = null;
  isCollapsed = false;
  openMap: { [name: string]: boolean } = {
    sub1: false,
    sub2: false,
    sub3: false,
    sub4: false
  };
  isFavorite = false;
  hasSynapseScope = false;
  hasSynapseViewAllUsers = false;
  hasSynapseViewAllOrganizations = false;
  hasSynapseManagerAllOrganizations = false;
  hasSynapseDeleteOrganizations = false;
  organizationsMenu: ExtendedOrganization[] = [];

  constructor(
    private router: Router,
    private organizationsService: OrganizationsService,
    private authService: AuthService,
    private message: NzMessageService
  ) {
    this.loadPermissions();
  }

  ngOnInit() {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.checkActiveMenu();
      // Save permissions to localStorage when navigating to /organizations
      if (event.urlAfterRedirects.includes('/organizations')) {
        this.authService.permissions$.subscribe((permissionsResponse: any) => {
          const orgPermissions = permissionsResponse?.data?.permissions
            ?.filter((perm: any) => perm.scope === 'org')
            .flatMap((perm: any) => perm.permissions) || [];
          const relevantPermissions = orgPermissions.filter((perm: string) =>
            ['synapse_view_organizations', 'synapse_manage_organizations', 'synapse_delete_organizations'].includes(perm)
          );
          localStorage.setItem('organizationPermissionsSynapse', JSON.stringify(relevantPermissions));
        });
      }
    });
  }

  private loadPermissions() {
    this.authService.permissions$.subscribe({
      next: (response: any) => {
        if (!response || !Array.isArray(response.data.permissions)) {
          this.hasSynapseScope = false;
          this.hasSynapseViewAllUsers = false;
          this.hasSynapseViewAllOrganizations = false;
          this.hasSynapseManagerAllOrganizations = false;
          this.hasSynapseDeleteOrganizations = false;
          this.organizationsMenu = [];
          return;
        }
        const permissions = response.data.permissions;

        // Check if the user has the 'org' scope for Synapse membership
        this.hasSynapseScope = permissions.some(
          (perm: any) => perm.scope === 'synapse'
        );

        // Get organizationId from scope: "org"
        const orgIds = [...new Set(
          permissions
            .filter((perm: any) => perm.scope === 'org')
            .map((perm: any) => perm.organizationId)
        )];

        const orgPermissions = permissions.reduce((acc: { [key: string]: any[] }, perm: any) => {
          if (!acc[perm.organizationId]) {
            acc[perm.organizationId] = [];
          }
          acc[perm.organizationId].push(perm);
          return acc;
        }, {});

        // Check permissions in scope 'org'
        this.hasSynapseViewAllOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.scope === 'org' && perm.permissions.includes('synapse_view_organizations')
          )
        );
        this.hasSynapseManagerAllOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.scope === 'org' && perm.permissions.includes('synapse_manage_organizations')
          )
        );
        this.hasSynapseDeleteOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.scope === 'org' && perm.permissions.includes('synapse_delete_organizations')
          )
        );

        // Check permissions in scope 'synapse' for users
        const synapseOrgIds = [...new Set(
          permissions
            .filter((perm: any) => perm.scope === 'synapse')
            .map((perm: any) => perm.organizationId)
        )];
        this.hasSynapseViewAllUsers = synapseOrgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.scope === 'synapse' && perm.permissions.includes('synapse_view_synapse_users')
          )
        );

        // Filter orgIds to exclude those with scope 'synapse' for organizationsMenu
        const synapseOrgIdsSet = new Set(synapseOrgIds);
        const nonSynapseOrgIds = [...new Set(
          permissions
            .map((perm: any) => perm.organizationId)
            .filter((orgId: string) => !synapseOrgIdsSet.has(orgId))
        )] as string[];

        if (nonSynapseOrgIds.length === 0) {
          this.organizationsMenu = [];
          return;
        }

        // Fetch organization details for non-synapse orgIds
        const orgRequests = nonSynapseOrgIds.map((orgId: string) =>
          this.organizationsService.getOrganizationsId(orgId)
        );

        forkJoin(orgRequests).subscribe({
          next: (orgs: any) => {
            this.organizationsMenu = orgs
              .filter((org: any): org is Organization => org != null && !!org.id && !!org.name)
              .map((org: any) => ({
                ...org,
                hasDeviceGroupScope: (orgPermissions[org.id] || []).some(
                  (perm: any) => perm.scope === 'device_group'
                ),
                hasLocationGroupScope: (orgPermissions[org.id] || []).some(
                  (perm: any) => perm.scope === 'location_group'
                ),
                hasOrgManageDevices: (orgPermissions[org.id] || []).some(
                  (perm: any) => perm.scope === 'org' && perm.permissions.includes('org_manage_devices')
                )
              }))
              .sort((a: any, b: any) => a.name.localeCompare(b.name));
            console.log('Organizations Menu:', this.organizationsMenu);
          },
          error: (err) => {
            console.error('Error fetching organizationsMenu:', err);
            this.message.create('error', 'Failed to load organizationsMenu. Please try again.', { nzDuration: 5000 });
            this.organizationsMenu = [];
          }
        });
      },
      error: (err) => {
        this.hasSynapseScope = false;
        this.hasSynapseViewAllUsers = false;
        this.hasSynapseViewAllOrganizations = false;
        this.hasSynapseManagerAllOrganizations = false;
        this.hasSynapseDeleteOrganizations = false;
        this.organizationsMenu = [];
      }
    });
  }

  openHandler(key: string, open?: boolean): void {
    if (open !== undefined) {
      this.openMap[key] = open;
    } else {
      this.openMap[key] = true;
    }
  }

  private checkActiveMenu(): void {
    const activeRoutes = ['/permissions', '/users'];
    if (activeRoutes.some((route) => this.isActive(route))) {
      this.openMap['sub1'] = true;
      this.openHandler('sub1');
    } else {
      this.openMap['sub1'] = false;
    }
  }

  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  isActive(route: string): boolean {
    return this.router.url.includes(route);
  }

  handleFavorite() {
    this.isFavorite = !this.isFavorite;
  }
}