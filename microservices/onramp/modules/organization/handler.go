package organization

import (
	"net/http"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	organizationDomain "synapse-its.com/shared/rest/domain/organization"
	RestDevicegroups "synapse-its.com/shared/rest/onramp/devicegroups"
	RestInvites "synapse-its.com/shared/rest/onramp/invites"
	RestOrganization "synapse-its.com/shared/rest/onramp/organization"
	RestPermissions "synapse-its.com/shared/rest/onramp/permissions"
	RestRoles "synapse-its.com/shared/rest/onramp/roles"
)

// APSFactoryResetService defines the interface for APS factory reset operations
type apsFactoryResetService interface {
	GetAPSFactorResetCode(userID, orgID uuid.UUID, challengeCode string) (string, error)
}

type membershipService interface {
	GetUsersByOrganizationID(organizationID uuid.UUID) ([]*organizationDomain.Membership, error)
	UpdateUserRoleInOrganization(userID uuid.UUID, organizationID uuid.UUID, roleID uuid.UUID) error
	DeleteUserFromOrganization(userID uuid.UUID, organizationID uuid.UUID) error
}

type Handler struct {
	apsFactoryResetService apsFactoryResetService
	membershipBiz          membershipService
}

func NewHandler(apsFactoryResetService apsFactoryResetService, membershipBiz membershipService) *Handler {
	return &Handler{
		apsFactoryResetService: apsFactoryResetService,
		membershipBiz:          membershipBiz,
	}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)
	router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.GetRolesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.CreateRoleHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations/{organizationId}/roles/{roleId}", RestRoles.DeleteRoleHandler).Methods(http.MethodDelete)
	router.HandleFunc("/organizations/{organizationId}/role-templates", RestRoles.GetRoleTemplatesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/permissions", RestPermissions.GetPermissionsHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/permissions/{permissionId}/roles/{roleId}", RestPermissions.UpdatePermissionHandler).Methods(http.MethodPatch)

	// Add new invite routes
	router.HandleFunc("/organizations/{organizationId}/invites", RestInvites.CreateInviteHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations/{organizationId}/invites", RestInvites.ListUserInvitesForOrganizationHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/invites/{inviteId}", RestInvites.RevokeInviteHandler).Methods(http.MethodDelete)
	router.HandleFunc("/organizations/{organizationId}/invites/validate", RestInvites.ValidateInviteHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/invites/{inviteId}/resend", RestInvites.ResendInviteHandler).Methods(http.MethodPost)

	// CRUD for device groups
	router.HandleFunc("/organizations/{organizationId}/devicegroups", RestDevicegroups.GetDeviceGroupsHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/devicegroups", RestDevicegroups.CreateDeviceGroupsHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations/{organizationId}/devicegroups/{devicegroupId}", RestDevicegroups.UpdateDeviceGroupsHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organizations/{organizationId}/devicegroups/{devicegroupId}", RestDevicegroups.DeleteDeviceGroupsHandler).Methods(http.MethodDelete)
	// Add new membership routes
	router.HandleFunc("/organizations/{organizationId}/users", h.getUsersByOrganizationID).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/users/{userId}", h.updateUserRoleInOrganization).Methods(http.MethodPatch)
	router.HandleFunc("/organizations/{organizationId}/users/{userId}", h.deleteUserFromOrganization).Methods(http.MethodDelete)

	// APS factory reset route
	router.HandleFunc("/organizations/{organizationId}/aps-factory-reset", h.handleAPSFactorReset).Methods(http.MethodPost)
}
