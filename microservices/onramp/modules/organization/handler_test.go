package organization

import (
	"net/http"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
)

func TestNewHandler(t *testing.T) {
	t.<PERSON>llel()

	// Arrange
	mockAPS := &mockAPSFactoryResetService{}
	mockMembership := &MockMembershipService{}

	// Act
	handler := NewHandler(mockAPS, mockMembership)

	// Assert
	assert.NotNil(t, handler, "Hand<PERSON> should not be nil")
	assert.Equal(t, mockAPS, handler.apsFactoryResetService, "APS factory reset service should be set correctly")
	assert.Equal(t, mockMembership, handler.membershipBiz, "Membership business service should be set correctly")
}

func TestHandler_RegisterRoutes(t *testing.T) {
	t.Parallel()

	// Arrange
	handler := &Handler{}
	router := mux.NewRouter()

	// Act
	handler.RegisterRoutes(router)

	// Expected routes based on RegisterRoutes implementation
	expectedRoutes := []struct {
		method string
		path   string
	}{
		// Organization routes
		{http.MethodPost, "/organizations"},
		{http.MethodGet, "/organizations"},
		{http.MethodGet, "/organizations/{identifier}"},
		{http.MethodPatch, "/organizations/{identifier}"},
		{http.MethodDelete, "/organizations/{identifier}"},

		// Role routes
		{http.MethodGet, "/organizations/{organizationId}/roles"},
		{http.MethodPost, "/organizations/{organizationId}/roles"},
		{http.MethodDelete, "/organizations/{organizationId}/roles/{roleId}"},
		{http.MethodGet, "/organizations/{organizationId}/role-templates"},

		// Permission routes
		{http.MethodGet, "/organizations/{organizationId}/permissions"},
		{http.MethodPatch, "/organizations/{organizationId}/permissions/{permissionId}/roles/{roleId}"},

		// Invite routes
		{http.MethodPost, "/organizations/{organizationId}/invites"},
		{http.MethodGet, "/organizations/{organizationId}/invites"},
		{http.MethodDelete, "/organizations/{organizationId}/invites/{inviteId}"},
		{http.MethodGet, "/organizations/{organizationId}/invites/validate"},
		{http.MethodPost, "/organizations/{organizationId}/invites/{inviteId}/resend"},

		// Membership routes
		{http.MethodGet, "/organizations/{organizationId}/users"},
		{http.MethodPatch, "/organizations/{organizationId}/users/{userId}"},
		{http.MethodDelete, "/organizations/{organizationId}/users/{userId}"},

		// APS factory reset route
		{http.MethodPost, "/organizations/{organizationId}/aps-factory-reset"},

		// Device group routes
		{http.MethodGet, "/organizations/{organizationId}/devicegroups"},
		{http.MethodPost, "/organizations/{organizationId}/devicegroups"},
		{http.MethodPatch, "/organizations/{organizationId}/devicegroups/{devicegroupId}"},
		{http.MethodDelete, "/organizations/{organizationId}/devicegroups/{devicegroupId}"},
	}

	// Assert
	// Count total routes registered
	var actualRoutes []struct {
		method string
		path   string
	}

	err := router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		pathTemplate, _ := route.GetPathTemplate()
		methods, _ := route.GetMethods()

		for _, method := range methods {
			actualRoutes = append(actualRoutes, struct {
				method string
				path   string
			}{
				method: method,
				path:   pathTemplate,
			})
		}
		return nil
	})

	assert.NoError(t, err, "Walking routes should not produce an error")

	// Verify expected routes are registered
	assert.Equal(t, len(expectedRoutes), len(actualRoutes), "Should register correct number of routes")

	// Check that all expected routes and methods are present
	for _, expectedRoute := range expectedRoutes {
		found := false
		for _, actualRoute := range actualRoutes {
			if actualRoute.method == expectedRoute.method && actualRoute.path == expectedRoute.path {
				found = true
				break
			}
		}
		assert.True(t, found, "Route %s %s should be registered", expectedRoute.method, expectedRoute.path)
	}
}
