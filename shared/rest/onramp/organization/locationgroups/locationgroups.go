package locationgroups

import (
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/helper"
)

type HandlerDeps struct {
	GetConnections                    func(context.Context, ...bool) (*connect.Connections, error)
	CreateLocationGroup               func(connect.DatabaseExecutor, uuid.UUID, *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error)
	GetLocationGroupsByOrganizationID func(connect.DatabaseExecutor, uuid.UUID) (*LocationGroupsResponse, error)
	UpdateLocationGroup               func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error)
	DeleteLocationGroup               func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
}

// Constants
const (
	organizationIDParam  = "organizationId"
	locationGroupIDParam = "locationgroupId"
)

func CreateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate organization is existed or deleted
		err = helper.ValidateOrganizationExistedOrDeleted(pg, organizationID)
		if err != nil {
			logger.Errorf("Error validating organization: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		req, err := parseCreateAndUpdateLocationGroupsRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Create the location group
		locationGroupResponse, err := deps.CreateLocationGroup(pg, organizationID, req)
		if err != nil {
			logger.Errorf("Error creating location group: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the location group
		response.CreateSuccessResponse(locationGroupResponse, w)
	}
}

// Get location groups by organization ID
func GetLocationGroupsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the location groups by organization ID
		locationGroupsResponse, err := deps.GetLocationGroupsByOrganizationID(pg, organizationID)
		if err != nil {
			logger.Errorf("Error getting location groups: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the location groups
		response.CreateSuccessResponse(locationGroupsResponse, w)
	}
}

// Update a location group
func UpdateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate organization is existed or deleted
		err = helper.ValidateOrganizationExistedOrDeleted(pg, organizationID)
		if err != nil {
			logger.Errorf("Error validating organization: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request URL
		locationGroupID, err := helper.ParseUUIDFromRequest(r, locationGroupIDParam)
		if err != nil {
			logger.Errorf("Error parsing location group ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		req, err := parseCreateAndUpdateLocationGroupsRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Update the location group
		locationGroupResponse, err := deps.UpdateLocationGroup(pg, organizationID, locationGroupID, req)
		if err != nil {
			logger.Errorf("Error updating location group: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the location group
		response.CreateSuccessResponse(locationGroupResponse, w)
	}
}

// Delete a location group
func DeleteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate organization is existed or deleted
		err = helper.ValidateOrganizationExistedOrDeleted(pg, organizationID)
		if err != nil {
			logger.Errorf("Error validating organization: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request URL
		locationGroupID, err := helper.ParseUUIDFromRequest(r, locationGroupIDParam)
		if err != nil {
			logger.Errorf("Error parsing location group ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Delete the location group
		err = deps.DeleteLocationGroup(pg, organizationID, locationGroupID)
		if err != nil {
			logger.Errorf("Error deleting location group: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the location group
		response.CreateSuccessResponse(nil, w)
	}
}

// Parse the request body for creating a location group
func parseCreateAndUpdateLocationGroupsRequest(r *http.Request) (*CreateAndUpdateLocationGroupsRequest, error) {
	var req CreateAndUpdateLocationGroupsRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unexpected fields

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse create request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRequestBody
	}

	// Validate the name is not empty
	if strings.TrimSpace(req.Name) == "" {
		return &req, ErrInvalidDescription
	}
	req.Name = strings.TrimSpace(req.Name)

	return &req, nil
}

// Create a location group
func createLocationGroup(pg connect.DatabaseExecutor, organizationID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
	now := time.Now().UTC()
	query := `
		INSERT INTO {{LocationGroups}} (
			Id,
			OrganizationId,
			Name,
			IsDeleted,
			CreatedAt,
			UpdatedAt
		)
		VALUES (
			uuid_generate_v4(),
			$1,
			$2,
			false,
			$3,
			$3
		)
		RETURNING Id, OrganizationId, Name, CreatedAt, UpdatedAt
	`
	var locationGroupResponse LocationGroupResponse
	err := pg.QueryRowStruct(&locationGroupResponse, query, organizationID, req.Name, now)
	if err != nil {
		logger.Errorf("failed to create location group: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &locationGroupResponse, nil
}

// Get location groups by organization ID
func getLocationGroupsByOrganizationID(pg connect.DatabaseExecutor, organizationID uuid.UUID) (*LocationGroupsResponse, error) {
	query := `
		SELECT
		 Id,
		OrganizationId, 
		Name, 
		CreatedAt, 
		UpdatedAt
		FROM {{LocationGroups}}
		WHERE OrganizationId = $1 AND IsDeleted = false
	`
	var locationGroupsResponse LocationGroupsResponse
	err := pg.QueryGenericSlice(&locationGroupsResponse.LocationGroups, query, organizationID)
	if err != nil {
		logger.Errorf("failed to get location groups: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &locationGroupsResponse, nil
}

// Update a location group
func updateLocationGroup(pg connect.DatabaseExecutor, organizationID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
	now := time.Now().UTC()
	query := `
		UPDATE {{LocationGroups}}
		SET
		 Name = $1,
		 UpdatedAt = $2
		WHERE Id = $3 AND OrganizationId = $4 AND IsDeleted = false
		RETURNING Id, OrganizationId, Name, CreatedAt, UpdatedAt
	`
	var locationGroupResponse LocationGroupResponse
	err := pg.QueryRowStruct(&locationGroupResponse, query, req.Name, now, locationGroupID, organizationID)
	if err != nil {
		if err == sql.ErrNoRows {
			logger.Errorf("location group not found: %v", err)
			return nil, ErrLocationGroupNotFound
		}
		logger.Errorf("failed to update location group: %v", err)
		return &locationGroupResponse, ErrDatabaseOperation
	}

	return &locationGroupResponse, nil
}

// Delete a location group
func deleteLocationGroup(pg connect.DatabaseExecutor, organizationID uuid.UUID, locationGroupID uuid.UUID) error {
	now := time.Now().UTC()
	query := `
		UPDATE {{LocationGroups}}
		SET 
			IsDeleted = true, 
			UpdatedAt = $1
		WHERE 
			Id = $2
			AND OrganizationId = $3 
			AND IsDeleted = false
	`
	_, err := pg.Exec(query, now, locationGroupID, organizationID)
	if err != nil {
		if err == sql.ErrNoRows {
			logger.Errorf("location group not found: %v", err)
			return ErrLocationGroupNotFound
		}
		logger.Errorf("failed to delete location group: %v", err)
		return ErrDatabaseOperation
	}

	return nil
}

// Handler is the production-ready HTTP handler using default dependencies.
var (
	CreateHandler = CreateHandlerWithDeps(HandlerDeps{
		GetConnections:      connect.GetConnections,
		CreateLocationGroup: createLocationGroup,
	})

	GetLocationGroupsHandler = GetLocationGroupsHandlerWithDeps(HandlerDeps{
		GetConnections:                    connect.GetConnections,
		GetLocationGroupsByOrganizationID: getLocationGroupsByOrganizationID,
	})

	UpdateHandler = UpdateHandlerWithDeps(HandlerDeps{
		GetConnections:      connect.GetConnections,
		UpdateLocationGroup: updateLocationGroup,
	})

	DeleteHandler = DeleteHandlerWithDeps(HandlerDeps{
		GetConnections:      connect.GetConnections,
		DeleteLocationGroup: deleteLocationGroup,
	})
)
